@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
	/* ===== CORES PRINCIPAIS ===== */
	--primary: #004475;
	--primary-foreground: #ffffff;
	--secondary: #f1f5f9;
	--secondary-foreground: #1e293b;

	/* ===== CORES DE FUNDO ===== */
	--background: #f9f9fa;
	--background-secondary: #eceef2;
	--foreground: #000000;

	/* ===== CORES DE TEXTO ===== */
	--text-primary: #000000;
	--text-secondary: #514d4d;

	/* ===== CORES DE BORDA E INPUT ===== */
	--border: #e0e0e0;
	--input: #e0e0e0;
	--ring: #004475;

	/* ===== CORES DE ACENTO ===== */
	--accent: #f1f5f9;
	--accent-foreground: #1e293b;
	--muted: #f1f5f9;
	--muted-foreground: #64748b;

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--destructive: #ef4444;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS ===== */
	--leaf-green-color: #00a03c;

	/* ===== CORES DE CARD (MANTIDAS COMO ESTÃO) ===== */
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.129 0.042 264.695);

	/* ===== CORES DE POPOVER ===== */
	--popover: #ffffff;
	--popover-foreground: #000000;

	/* ===== CORES DE CHART ===== */
	--chart-1: #3b82f6;
	--chart-2: #10b981;
	--chart-3: #f59e0b;
	--chart-4: #ef4444;
	--chart-5: #8b5cf6;

	/* ===== CORES DE SIDEBAR ===== */
	--sidebar: #ffffff;
	--sidebar-foreground: #000000;
	--sidebar-primary: #004475;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #f1f5f9;
	--sidebar-accent-foreground: #1e293b;
	--sidebar-border: #e0e0e0;
	--sidebar-ring: #004475;

	/* ===== CORES DA LOGO ===== */
	--logo-background: #004475;
	--logo-leaf-green: #00a03c;

	/* ===== RADIUS ===== */
	--radius: 0.938rem;
	--radius-controls: 0.625rem;
}

/* ===== TEMA DARK ===== */
.dark {
	/* ===== CORES PRINCIPAIS DARK ===== */
	--primary: #0066cc;
	--primary-foreground: #ffffff;
	--secondary: #1e2a3a;
	--secondary-foreground: #e2e8f0;

	/* ===== CORES DE FUNDO DARK ===== */
	--background: #0f1419;
	--background-secondary: #1a202c;
	--foreground: #ffffff;

	/* ===== CORES DE TEXTO DARK ===== */
	--text-primary: #ffffff;
	--text-secondary: #cbd5e0;

	/* ===== CORES DE BORDA E INPUT DARK ===== */
	--border: #2d3748;
	--input: #1e2a3a;
	--ring: #0066cc;

	/* ===== CORES DE ACENTO DARK ===== */
	--accent: #1e2a3a;
	--accent-foreground: #e2e8f0;
	--muted: #1e2a3a;
	--muted-foreground: #a0aec0;

	/* ===== CORES DE DESTRUIÇÃO DARK ===== */
	--destructive: #ff5555;
	--destructive-foreground: #ffffff;

	/* ===== CORES ESPECÍFICAS DARK ===== */
	--leaf-green-color: #00d147;

	/* ===== CORES DE CARD DARK ===== */
	--card: #1a202c;
	--card-foreground: #ffffff;

	/* ===== CORES DE POPOVER DARK ===== */
	--popover: #1e2a3a;
	--popover-foreground: #ffffff;

	/* ===== CORES DE CHART DARK (MAIS VIBRANTES) ===== */
	--chart-1: #4facfe;
	--chart-2: #00f5a0;
	--chart-3: #ffb347;
	--chart-4: #ff6b6b;
	--chart-5: #a78bfa;

	/* ===== CORES DE SIDEBAR DARK ===== */
	--sidebar: #141b26;
	--sidebar-foreground: #ffffff;
	--sidebar-primary: #0066cc;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #1e2a3a;
	--sidebar-accent-foreground: #e2e8f0;
	--sidebar-border: #2d3748;
	--sidebar-ring: #0066cc;

	/* ===== CORES DA LOGO DARK ===== */
	--logo-background: #ffffff;
	--logo-leaf-green: #00d147;
}

@layer base {
	[data-theme="light"] {
		--color-primary: #004475;
		--color-background: #ffffff;
		--color-leaf-green-color: #00a03c;
		--color-sidebar-group-title: #777790;
		--color-logo-background: #004475;
		--color-logo-leaf-green: #00a03c;
	}

	[data-theme="blue-dark"] {
		--color-primary: #004475;
		--color-background: #000000;
		--color-leaf-green-color: #00a03c;
		--color-logo-background: #ffffff;
		--color-logo-leaf-green: #00a03c;
	}

	/* ===== NOVO TEMA DARK ===== */
	[data-theme="dark"] {
		--color-primary: #0066cc;
		--color-background: #0f1419;
		--color-leaf-green-color: #00d147;
		--color-sidebar-group-title: #a0aec0;
		--color-logo-background: #0066cc;
		--color-logo-leaf-green: #00d147;
	}
}

@theme inline {
	/* ===== CORES PRINCIPAIS ===== */
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);

	--color-logo-background: var(--logo-background);
	--color-logo-leaf-green: var(--logo-leaf-green);

	/* ===== CORES DE FUNDO ===== */
	--color-background: var(--background);
	--color-background-secondary: var(--background-secondary);
	--color-foreground: var(--foreground);

	/* ===== CORES DE TEXTO ===== */
	--color-text-primary: var(--text-primary);
	--color-text-secondary: var(--text-secondary);

	/* ===== CORES DE BORDA E INPUT ===== */
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);

	/* ===== CORES DE ACENTO ===== */
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);

	/* ===== CORES DE DESTRUIÇÃO ===== */
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);

	/* ===== CORES ESPECÍFICAS ===== */
	--color-leaf-green-color: var(--leaf-green-color);

	/* ===== CORES DE CARD ===== */
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);

	/* ===== CORES DE POPOVER ===== */
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);

	/* ===== CORES DE CHART ===== */
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);

	/* ===== CORES DE SIDEBAR ===== */
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	/* ===== RADIUS ===== */
	--radius-main: var(--radius);
	--radius-controls: var(--radius-controls);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
		/* Transição suave para mudança de tema */
		transition:
			background-color 0.3s ease,
			color 0.3s ease;
	}

	/* ===== ESTILOS ESPECÍFICOS PARA TEMA DARK ===== */
	.dark {
		/* Melhora o contraste para textos */
		color-scheme: dark;
	}

	/* Scrollbar personalizada para tema dark */
	.dark ::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}

	.dark ::-webkit-scrollbar-track {
		background: #1a202c;
	}

	.dark ::-webkit-scrollbar-thumb {
		background: #4a5568;
		border-radius: 4px;
	}

	.dark ::-webkit-scrollbar-thumb:hover {
		background: #0066cc;
	}

	/* Estados de hover e focus melhorados para dark mode */
	.dark button:hover {
		filter: brightness(1.1);
		box-shadow: 0 0 0 1px rgba(0, 102, 204, 0.3);
	}

	.dark input:focus,
	.dark textarea:focus,
	.dark select:focus {
		box-shadow: 0 0 0 2px var(--ring);
		border-color: #0066cc;
	}

	/* Melhorias visuais para cards e elementos em dark mode */
	.dark .card,
	.dark [class*="card"] {
		background: var(--card);
		border: 1px solid var(--border);
		/* box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1); */
	}

	/* Tabelas em dark mode */
	.dark table {
		background: var(--card);
	}

	.dark th {
		background: var(--accent);
		border-color: var(--border);
	}

	.dark td {
		border-color: var(--border);
	}

	.dark tr:hover {
		background: rgba(0, 102, 204, 0.08);
	}

	Gradientes sutis para elementos principais
	/* .dark .sidebar,
	.dark [class*="sidebar"] {
		background: linear-gradient(180deg, #141b26 0%, #0f1419 100%);
	} */

	/* Efeitos de glow sutis para elementos interativos */
	.dark button:focus,
	.dark [role="button"]:focus {
		box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.4);
	}

	/* Melhor contraste para links */
	.dark a {
		color: #66b3ff;
	}

	.dark a:hover {
		color: #99ccff;
	}

	/* Elementos de status com toque azul */
	.dark .badge,
	.dark [class*="badge"] {
		background: rgba(0, 102, 204, 0.15);
		border: 1px solid rgba(0, 102, 204, 0.3);
	}
}
