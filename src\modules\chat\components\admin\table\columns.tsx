import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { Badge } from "@/shared/components/shadcn/badge";
import { ColumnDef } from "@tanstack/react-table";
import { CheckCircle, FileText, XCircle } from "lucide-react";
import { ChatKnowledgeActions } from "./actions";

export const chatAdminColumns: ColumnDef<IChatKnowledgeDto>[] = [
	{
		accessorKey: "title",
		header: () => <div className="px-6 text-left font-semibold text-white">Título</div>,
		cell: ({ row }) => (
			<div className="flex items-center gap-3 px-6 py-4">
				<div className="bg-primary/10 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg">
					<FileText className="text-primary h-5 w-5" />
				</div>
				<div className="min-w-0 flex-1">
					<p className="truncate text-sm font-semibold text-gray-900" title={row.original.title}>
						{row.original.title}
					</p>
					<p className="text-xs text-gray-500">Conhecimento do chatbot</p>
				</div>
			</div>
		),
		size: 400,
	},
	{
		accessorKey: "isActive",
		header: () => <div className="px-6 text-center font-semibold text-white">Status</div>,
		cell: ({ row }) => (
			<div className="flex justify-center px-6 py-4">
				<Badge
					variant={row.original.isActive ? "default" : "secondary"}
					className={`inline-flex items-center gap-1.5 rounded-full px-3 py-1 text-xs font-medium ${
						row.original.isActive
							? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
							: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
					}`}
				>
					{row.original.isActive ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
					{row.original.isActive ? "Ativo" : "Inativo"}
				</Badge>
			</div>
		),
		size: 150,
	},
	{
		accessorKey: "createdAt",
		header: () => <div className="px-6 text-center font-semibold text-white">Criado em</div>,
		cell: ({ row }) => (
			<div className="flex flex-col items-center justify-center px-6 py-4">
				<span className="text-sm font-medium text-gray-900">
					{new Date(row.original.createdAt).toLocaleDateString("pt-BR", {
						day: "2-digit",
						month: "2-digit",
						year: "numeric",
					})}
				</span>
				<span className="text-xs text-gray-500">
					{new Date(row.original.createdAt).toLocaleTimeString("pt-BR", {
						hour: "2-digit",
						minute: "2-digit",
					})}
				</span>
			</div>
		),
		size: 180,
	},
	{
		id: "actions",
		header: () => <div className="px-6 text-center font-semibold text-white">Ações</div>,
		cell: ({ row }) => (
			<div className="flex justify-center px-6 py-4">
				<ChatKnowledgeActions knowledgeId={String(row.original.id)} title={row.original.title} />
			</div>
		),
		size: 120,
	},
];
