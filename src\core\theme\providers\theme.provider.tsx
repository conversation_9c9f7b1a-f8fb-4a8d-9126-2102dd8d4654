"use client";

import { use<PERSON><PERSON>, useAtomValue, useSetAtom } from "jotai";
import { PropsWithChildren, useEffect, useLayoutEffect, useRef } from "react";
import { applyTheme, getCurrentTheme, isThemeVariant, mountedAtom, themeAtom, userExplicitThemeAtom, type ThemeVariant } from "../atoms/theme.atom";

const ThemeManager = () => {
	const [mounted] = useAtom(mountedAtom);
	const explicit = useAtomValue(userExplicitThemeAtom);
	const current = useAtomValue(getCurrentTheme);
	const setTheme = useSetAtom(applyTheme);

	useEffect(() => {
		if (explicit) return;
		if (typeof window === "undefined" || typeof window.matchMedia !== "function") return;
		const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

		const handleChange = () => {
			if (!mounted && !explicit) {
				setTheme(mediaQuery.matches ? "dark" : "light");
			}
		};

		mediaQuery.addEventListener("change", handleChange);
		return () => mediaQuery.removeEventListener("change", handleChange);
	}, [mounted, explicit, setTheme]);

	useEffect(() => {
		if (typeof document === "undefined") return;
		if (!document.documentElement.classList.contains(current)) {
			setTheme(current);
		}
	}, [current, setTheme]);

	return null;
};

export const ThemeProvider = ({ children }: PropsWithChildren) => {
	const setTheme = useSetAtom(applyTheme);
	const appliedRef = useRef<ThemeVariant | null>(null);
	const [storedTheme] = useAtom(themeAtom);

	// Aplica tema o mais cedo possível (antes de paint) lendo directly localStorage se necessário
	useLayoutEffect(() => {
		const resolveTheme = (): ThemeVariant => {
			if (isThemeVariant(storedTheme)) return storedTheme;
			if (typeof window !== "undefined") {
				try {
					const raw = window.localStorage.getItem("simp-theme");
					if (raw) {
						const parsed = JSON.parse(raw);
						if (isThemeVariant(parsed)) return parsed;
					}
				} catch {}
				const prefersDark = window.matchMedia?.("(prefers-color-scheme: dark)")?.matches;
				return prefersDark ? "dark" : "light";
			}
			return "light";
		};
		const resolved = resolveTheme();
		if (appliedRef.current !== resolved) {
			appliedRef.current = resolved;
			setTheme(resolved);
		}
	}, [storedTheme, setTheme]);

	return (
		<>
			<ThemeManager />
			{children}
		</>
	);
};

export { ThemeManager };
