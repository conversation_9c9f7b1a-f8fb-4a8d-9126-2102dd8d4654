"use client";
import { pathService } from "@/config/path-manager/service";
import { BotIcon } from "lucide-react";
import { ComponentType, useState } from "react";
import { useTableChatAdmin } from "../../../hooks/list/table-chat-admin.hook";
import { EmptyState } from "../_components/empty-state";
import { EditKnowledgeModal } from "../edit/edit-knowledge-modal";
import { ChatAiAdminHeader } from "../header";
import { DesktopTableView } from "./desktop-table-view";
import { MobileTableView } from "./mobile-table-view";

interface TableChatAdminProps {
	onOpenModal: () => void;
}

export const TableChatAdmin = ({ onOpenModal }: TableChatAdminProps) => {
	const [searchTerm, setSearchTerm] = useState("");
	const item = pathService.getItemById("chat");
	const Icon = item?.icon as ComponentType<unknown> | undefined;
	const {
		data,
		isLoading,
		error,
		hasError,
		pagination,
		pageSize,
		isMobile,
		canUpdate,
		editKnowledge,
		handleRowClick,
		handlePageSizeChange,
		setCurrentPage,
		isEmpty,
	} = useTableChatAdmin({ searchTerm });

	const renderTable = () => {
		if (isMobile) {
			return (
				<>
					<MobileTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
					/>
				</>
			);
		} else {
			return (
				<>
					<DesktopTableView
						data={data}
						isLoading={isLoading}
						hasError={hasError}
						error={error}
						searchTerm={searchTerm}
						pageSize={pageSize}
						pagination={pagination}
						onPageChange={setCurrentPage}
						onPageSizeChange={handlePageSizeChange}
						onRowClick={handleRowClick}
						canUpdate={canUpdate}
					/>
					{editKnowledge.isOpen && editKnowledge.selectedId && (
						<EditKnowledgeModal isOpen={editKnowledge.isOpen} onClose={editKnowledge.closeEditModal} id={editKnowledge.selectedId} />
					)}
				</>
			);
		}
	};

	console.log("isEmpty", isEmpty);
	console.log("data", data);

	return (
		<main className="flex flex-1 flex-col gap-4">
			<ChatAiAdminHeader
				icon={Icon ? <Icon /> : <BotIcon />}
				title="Chat Admin"
				description="Gerencie o conhecimento do chatbot de IA"
				search={searchTerm}
				onSearchChange={setSearchTerm}
				total={pagination?.totalItems || 0}
				onAdd={() => onOpenModal()}
			/>
			<div className="h-full">{isEmpty || data.length === 0 ? <EmptyState onAdd={() => onOpenModal()} /> : renderTable()}</div>
		</main>
	);
};
