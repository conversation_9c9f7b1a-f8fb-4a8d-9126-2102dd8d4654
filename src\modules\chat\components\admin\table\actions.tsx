import { ProtectedComponent } from "@/shared/components/auth/protected";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/components/shadcn/tooltip";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Trash2 } from "lucide-react";
import { DeleteKnowledgeModal } from "../delete/delete-knowledge-modal";

export const ChatKnowledgeActions = ({ knowledgeId, title }: { knowledgeId: string; title: string }) => {
	const deleteModal = useModal();

	return (
		<div className="flex items-center justify-center gap-2">
			<ProtectedComponent action="delete" subject="all">
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								data-row-ignore
								size="sm"
								variant="ghost"
								onClick={e => {
									e.stopPropagation();
									deleteModal.openModal();
								}}
								className="group h-8 w-8 rounded-lg bg-red-50 p-0 text-red-600 transition-all duration-200 hover:bg-red-100 hover:text-red-700 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<p>Excluir conhecimento</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</ProtectedComponent>
			{deleteModal.isOpen && <DeleteKnowledgeModal isOpen={deleteModal.isOpen} onClose={deleteModal.closeModal} title={title} id={knowledgeId} />}
		</div>
	);
};
