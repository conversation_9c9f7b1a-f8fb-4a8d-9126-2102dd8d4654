"use client";

import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

export type ThemeVariant = "light" | "dark" | "light-green" | "dark-green";

export const THEME_VARIANTS: ThemeVariant[] = ["light", "dark", "light-green", "dark-green"];

export const isThemeVariant = (value: unknown): value is ThemeVariant => typeof value === "string" && (THEME_VARIANTS as string[]).includes(value);
export const themeAtom = atomWithStorage<ThemeVariant>("simp-theme", "light", undefined, { getOnInit: true });
export const userExplicitThemeAtom = atom<boolean>(false);

export const getCurrentTheme = atom(get => get(themeAtom));

export const applyTheme = atom(null, (get, set, newTheme: ThemeVariant | null = null, options?: { explicit?: boolean }) => {
	const theme = newTheme ?? get(themeAtom);
	if (!isThemeVariant(theme)) return;
	document.documentElement.classList.remove(...THEME_VARIANTS);
	document.documentElement.classList.add(theme);
	set(themeAtom, theme);
	if (options?.explicit) set(userExplicitThemeAtom, true);
});

applyTheme.onMount = initialize => initialize();

export const mountedAtom = atom(false);
mountedAtom.onMount = set => set(true);
