import { IChatKnowledgeDto } from "@/modules/chat/types/dtos/find-all-knowledge.dto";
import { EmptyStateTable } from "@/shared/components/custom/empty/table-empty";
import { TableLoading } from "@/shared/components/custom/loading";
import { Pagination } from "@/shared/components/custom/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ComponentIcon } from "lucide-react";
import { chatAdminColumns } from "./columns";

interface DesktopTableViewProps {
	data: IChatKnowledgeDto[] | undefined;
	isLoading: boolean;
	hasError: boolean;
	error: string | false | undefined;
	searchTerm: string;
	pageSize: number;
	pagination: {
		currentPage: number;
		totalPages: number;
		itemsPerPage: number;
		totalItems: number;
	} | null;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
	onRowClick: (id: string) => void;
	canUpdate: boolean;
}

export const DesktopTableView = ({
	data,
	isLoading,
	hasError,
	error,
	searchTerm,
	pageSize,
	pagination,
	onPageChange,
	onPageSizeChange,
	onRowClick,
	canUpdate,
}: DesktopTableViewProps) => {
	const table = useReactTable({
		data: data ?? [],
		columns: chatAdminColumns,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const renderTableBody = () => {
		if (hasError) {
			return (
				<TableRow>
					<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center text-red-500">
						Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
					</TableCell>
				</TableRow>
			);
		}

		if (isLoading) {
			return <TableLoading columns={chatAdminColumns.length} rows={pageSize} />;
		}

		if (table.getRowModel().rows.length) {
			return table.getRowModel().rows.map((row, index) => {
				const clickable = canUpdate;
				return (
					<TableRow
						key={row.id}
						data-state={row.getIsSelected() && "selected"}
						className={`group border-b border-gray-100 transition-all duration-200 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-900/50 ${
							clickable ? "cursor-pointer" : ""
						} ${index % 2 === 0 ? "bg-white dark:bg-gray-950" : "bg-gray-50/50 dark:bg-gray-900/20"}`}
						onClick={() => {
							if (!clickable) return;
							onRowClick(String(row.original.id));
						}}
					>
						{row.getVisibleCells().map(cell => (
							<TableCell key={cell.id} className="border-0 p-0">
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				);
			});
		}

		return (
			<TableRow>
				<TableCell colSpan={chatAdminColumns.length} className="h-24 text-center">
					<EmptyStateTable
						searchTerm={searchTerm}
						icon={<ComponentIcon />}
						title="Nenhum conhecimento encontrado"
						description={searchTerm ? "Nenhum conhecimento corresponde ao termo pesquisado." : "Ainda não há conhecimentos cadastrados."}
						tip="Você pode tentar pesquisar por outros termos ou adicionar um novo conhecimento."
					/>
				</TableCell>
			</TableRow>
		);
	};

	return (
		<div className="flex h-full min-h-[500px] flex-col space-y-6">
			<div className="overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-950">
				<div className="overflow-x-auto">
					<Table className="w-full">
						<TableHeader className="from-primary to-primary/95 sticky top-0 z-10 bg-gradient-to-r">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id} className="border-0 hover:bg-transparent">
									{headerGroup.headers.map(header => {
										const colDef = header.column?.columnDef as unknown as { size?: number } | undefined;
										const style = colDef?.size && header.colSpan === 1 ? { width: `${colDef.size}px` } : undefined;
										return (
											<TableHead
												key={header.id}
												colSpan={header.colSpan}
												style={style}
												className="border-0 py-4 font-semibold text-white first:rounded-tl-xl last:rounded-tr-xl"
											>
												{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody className="divide-y-0">{renderTableBody()}</TableBody>
					</Table>
				</div>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					onPageChange={onPageChange}
					onPageSizeChange={size => {
						onPageSizeChange(size);
						onPageChange(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
