"use client";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { Input } from "@/shared/components/shadcn/input";
import { Plus, Search } from "lucide-react";
import { ReactNode } from "react";

interface AdminHeaderProps {
	icon?: ReactNode;
	title: string;
	description?: string;
	total?: number;
	search: string;
	onSearchChange: (v: string) => void;
	onAdd: () => void;
	childrenActions?: ReactNode;
}

export const ChatAiAdminHeader = ({ icon, title, description, total, search, onSearchChange, onAdd, childrenActions }: AdminHeaderProps) => {
	return (
		<div className="flex flex-col gap-4">
			<div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
				<div className="flex min-w-0 items-center gap-4">
					{icon ? (
						<div className="border-border bg-muted text-text-primary rounded-controls flex h-12 w-12 flex-shrink-0 items-center justify-center border">
							{icon}
						</div>
					) : null}
					<div className="min-w-0 space-y-1">
						<div className="flex flex-wrap items-center gap-2">
							<h1 className="text-text-primary truncate text-2xl leading-tight font-semibold">{title}</h1>
							{typeof total === "number" && (
								<span className="text-muted-foreground border-border bg-background/60 rounded-full border px-2 py-0.5 text-xs font-medium">
									{total} {total === 1 ? "item" : "conhecimentos"}
								</span>
							)}
						</div>
						{description && <p className="text-text-secondary truncate text-sm leading-snug">{description}</p>}
					</div>
				</div>
				<div className="flex w-full flex-col-reverse gap-2 sm:w-auto sm:flex-row sm:items-center sm:gap-3">
					<div className="relative w-full sm:min-w-[280px]">
						<Search className="text-muted-foreground absolute top-1/2 left-3 size-4 -translate-y-1/2" />
						<Input
							aria-label="Buscar conhecimento"
							placeholder="Buscar por título, tag ou status..."
							value={search}
							onChange={e => onSearchChange(e.target.value)}
							className="focus:border-primary/50 border-border h-11 w-full border-2 pr-3 pl-10 text-sm transition-colors"
						/>
					</div>
					<div className="flex gap-2">
						<Button onClick={onAdd} className="h-11 gap-2 shadow-sm transition-all hover:scale-[1.015] active:scale-100">
							<Plus className="size-4" />
							<span>Novo conhecimento</span>
						</Button>
						{childrenActions}
					</div>
				</div>
			</div>
		</div>
	);
};
